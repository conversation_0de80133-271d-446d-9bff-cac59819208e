<!-- Target Section Template for Professional Theme -->
<div class="target-section">
    <div class="card-header">
        <h2 class="card-title">
            🎯 {{ target_name }}
            {% if target_data.hostname and target_data.hostname != target_name %}
                <span class="text-muted">({{ target_data.hostname }})</span>
            {% endif %}
        </h2>
        <div class="controls">
            <span class="status status-{{ 'success' if target_data.status == 'complete' else 'info' }}">
                {{ target_data.status|title }}
            </span>
        </div>
    </div>

    <!-- Target Summary Stats -->
    {% if target_data.ports or target_data.services %}
    <div class="stats-grid mb-xl">
        <div class="stat-card">
            <div class="stat-number">{{ target_data.ports|length if target_data.ports else 0 }}</div>
            <div class="stat-label">Open Ports</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ target_data.web_services|length if target_data.web_services else 0 }}</div>
            <div class="stat-label">Web Services</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% set dir_count = 0 %}
                {% for web_service in target_data.web_services %}
                    {% if web_service.directories %}
                        {% set dir_count = dir_count + web_service.directories|length %}
                    {% endif %}
                {% endfor %}
                {{ dir_count }}
            </div>
            <div class="stat-label">Directories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ target_data.vulnerabilities|length if target_data.vulnerabilities else 0 }}</div>
            <div class="stat-label">Findings</div>
        </div>
    </div>
    {% endif %}

    <!-- KEY FINDINGS - Always visible at top -->
    <div class="card mb-xl" style="border: 1px solid rgba(59, 130, 246, 0.3); background: var(--bg-elevated); border-radius: var(--radius-lg); box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);">
        <div class="card-header" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1)); color: var(--text-primary); border-radius: var(--radius-lg) var(--radius-lg) 0 0; border-bottom: 1px solid rgba(59, 130, 246, 0.2);">
            <h3 class="mb-0">🔥 Key Findings</h3>
        </div>
        
        <!-- Open Ports - Always show if any -->
        {% if target_data.ports %}
        <div class="mb-lg">
            <h4 class="mb-md">🔍 Open Ports ({{ target_data.ports|length }})</h4>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Port</th>
                            <th>Service</th>
                            <th>Version</th>
                            <th>Protocol</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for port in target_data.ports %}
                        <tr>
                            <td><code style="font-weight: bold; color: var(--accent-primary);">{{ port.port }}</code></td>
                            <td>{{ port.service or 'Unknown' }}</td>
                            <td class="text-muted">{{ port.version or 'Not detected' }}</td>
                            <td><code>{{ port.protocol|upper }}</code></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Web Services with VHosts/Redirects and Directories -->
        {% if target_data.web_services %}
        <div class="mb-lg">
            <h4 class="mb-md">🌐 Web Services & Directory Discovery</h4>
            
            <!-- Debug info - shows if no directories/files are found -->
            {% set total_dirs = namespace(count=0) %}
            {% set total_files = namespace(count=0) %}
            {% for ws in target_data.web_services %}
                {% if ws.directories %}{% set total_dirs.count = total_dirs.count + ws.directories|length %}{% endif %}
                {% if ws.files %}{% set total_files.count = total_files.count + ws.files|length %}{% endif %}
            {% endfor %}
            
            {% if total_dirs.count == 0 and total_files.count == 0 %}
            <div class="card mb-md" style="border-left: 4px solid var(--accent-warning); background: rgba(245, 158, 11, 0.1);">
                <div class="card-header">
                    <h5 class="mb-0" style="color: var(--accent-warning);">⚠️ No Directory/File Discovery Results</h5>
                </div>
                <p class="mb-0">Directory busting may not have completed or found results. Check Raw Scan Data section below for dirbuster/feroxbuster output files.</p>
            </div>
            {% endif %}
            {% for web_service in target_data.web_services %}
            <div class="card mb-md" style="border-left: 4px solid var(--accent-success);">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <a href="{{ web_service.url }}" target="_blank" class="text-primary" style="font-size: 1.1em;">
                            {{ web_service.url }}
                        </a>
                        <span class="status status-{{ 'success' if web_service.status_code == 200 else 'warning' }}" style="margin-left: 10px;">
                            {{ web_service.status_code }}
                        </span>
                    </h5>
                    {% if web_service.title %}
                    <div class="card-subtitle">{{ web_service.title }}</div>
                    {% endif %}
                </div>
                
                <!-- Technologies on same line as URL -->
                {% if web_service.technologies %}
                <div class="mb-md">
                    <strong>Technologies:</strong>
                    {% for tech in web_service.technologies %}
                        <code class="text-primary">{{ tech }}</code>{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Directories - Always prominent with preview -->
                {% if web_service.directories and web_service.directories|length > 0 %}
                <div class="mb-md">
                    <h6 style="color: var(--accent-warning); font-weight: bold;">📁 Directories Found ({{ web_service.directories|length }})</h6>
                    
                    <!-- Show preview of first few directories -->
                    <div class="code-block mb-sm" style="max-height: 120px; overflow-y: auto; background: rgba(245, 158, 11, 0.1);">
                        <pre>{% for dir in web_service.directories[:5] %}{{ dir }}
{% endfor %}{% if web_service.directories|length > 5 %}
<span style="color: var(--text-muted);">... and {{ web_service.directories|length - 5 }} more directories</span>{% endif %}</pre>
                    </div>
                    
                    <!-- Expandable full list -->
                    {% if web_service.directories|length > 5 %}
                    <div class="collapsible collapsed">
                        <div class="collapsible-header">
                            <span>📋 View all {{ web_service.directories|length }} directories</span>
                            <span class="collapse-icon">▶</span>
                        </div>
                        <div class="collapsible-content">
                            <div class="code-block" style="max-height: 400px; overflow-y: auto;">
                                <pre>{% for dir in web_service.directories %}{{ dir }}
{% endfor %}</pre>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                <!-- Files - With preview -->
                {% if web_service.files and web_service.files|length > 0 %}
                <div class="mb-md">
                    <h6 style="color: var(--accent-info); font-weight: bold;">📄 Files Found ({{ web_service.files|length }})</h6>
                    
                    <!-- Show preview of first few files -->
                    <div class="code-block mb-sm" style="max-height: 120px; overflow-y: auto; background: rgba(6, 182, 212, 0.1);">
                        <pre>{% for file in web_service.files[:5] %}{{ file }}
{% endfor %}{% if web_service.files|length > 5 %}
<span style="color: var(--text-muted);">... and {{ web_service.files|length - 5 }} more files</span>{% endif %}</pre>
                    </div>
                    
                    <!-- Expandable full list -->
                    {% if web_service.files|length > 5 %}
                    <div class="collapsible collapsed">
                        <div class="collapsible-header">
                            <span>📋 View all {{ web_service.files|length }} files</span>
                            <span class="collapse-icon">▶</span>
                        </div>
                        <div class="collapsible-content">
                            <div class="code-block" style="max-height: 400px; overflow-y: auto;">
                                <pre>{% for file in web_service.files %}{{ file }}
{% endfor %}</pre>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Critical Findings -->
        {% if target_data.pattern_matches and target_data.pattern_matches|length > 0 %}
        {% set critical_matches = target_data.pattern_matches|selectattr('description')|list %}
        {% set critical_findings = [] %}
        {% for match in critical_matches %}
            {% if match.description and ('CRITICAL' in match.description or 'vuln' in match.description|lower or 'exploit' in match.description|lower) %}
                {% set _ = critical_findings.append(match) %}
            {% endif %}
        {% endfor %}
        {% if critical_findings|length > 0 %}
        <div class="mb-lg">
            <h4 class="mb-md" style="color: var(--accent-danger);">🚨 Critical Security Findings</h4>
            {% for match in critical_findings %}
            <div class="card mb-sm" style="border-left: 4px solid var(--accent-danger);">
                <div class="card-header">
                    <h5 class="card-title">{{ match.plugin_name or 'Security Scanner' }}</h5>
                    <span class="status status-danger">Critical</span>
                </div>
                <p class="mb-0">{{ match.description }}</p>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        {% endif %}
    </div>

    <!-- DETAILED SECTIONS - Collapsible -->
    
    <!-- Service Enumeration Results -->
    {% if target_data.services and target_data.services|length > 0 %}
    {% set valid_services = target_data.services|selectattr('name')|list %}
    {% if valid_services|length > 0 %}
    <div class="collapsible collapsed">
        <div class="collapsible-header">
            <h3 class="mb-0">🔧 Detailed Service Enumeration ({{ valid_services|length }} services)</h3>
            <span class="collapse-icon">▶</span>
        </div>
        <div class="collapsible-content">
            {% for service in valid_services %}
            {% if service.name and service.name.strip() %}
            <div class="card mb-lg">
                <div class="card-header">
                    <h4 class="card-title">
                        {{ service.name }} 
                        <span class="text-muted">on port {{ service.port }}</span>
                    </h4>
                    {% if service.version and service.version.strip() %}
                    <div class="card-subtitle">{{ service.version }}</div>
                    {% endif %}
                </div>
                
                {% if service.scan_results and service.scan_results.strip() %}
                <div class="code-block">
                    <div class="code-block-header">
                        <span class="code-title">Scan Results</span>
                        <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                    </div>
                    <pre>{{ service.scan_results }}</pre>
                </div>
                {% endif %}
                
                {% if service.vulnerabilities and service.vulnerabilities|length > 0 %}
                {% set valid_vulns = service.vulnerabilities|selectattr('title')|list %}
                {% if valid_vulns|length > 0 %}
                <div class="mt-md">
                    <h5>🚨 Vulnerabilities Found</h5>
                    <ul class="list-clean">
                        {% for vuln in valid_vulns %}
                        {% if vuln.title and vuln.title.strip() %}
                        <li>
                            <span class="status status-danger">{{ vuln.severity|upper if vuln.severity else 'UNKNOWN' }}</span>
                            <strong>{{ vuln.title }}</strong>
                            {% if vuln.description and vuln.description.strip() %}
                            <p class="text-muted mb-0">{{ vuln.description }}</p>
                            {% endif %}
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- All Security Findings - Enhanced and Organized -->
    {% if target_data.pattern_matches and target_data.pattern_matches|length > 0 %}
    {% set valid_matches = target_data.pattern_matches|selectattr('description')|list %}
    {% if valid_matches|length > 0 %}
    
    <!-- Categorize findings by severity -->
    {% set critical_findings = [] %}
    {% set warning_findings = [] %}
    {% set info_findings = [] %}
    {% for match in valid_matches %}
        {% if match.description and match.description.strip() %}
            {% if 'CRITICAL' in match.description or 'critical' in match.description|lower or 'vuln' in match.description|lower %}
                {% set _ = critical_findings.append(match) %}
            {% elif 'WARNING' in match.description or 'warning' in match.description|lower %}
                {% set _ = warning_findings.append(match) %}
            {% else %}
                {% set _ = info_findings.append(match) %}
            {% endif %}
        {% endif %}
    {% endfor %}
    
    <div class="collapsible">
        <div class="collapsible-header">
            <h3 class="mb-0">🔍 Security Findings Summary ({{ valid_matches|length }} total)</h3>
            <span class="collapse-icon">▼</span>
        </div>
        <div class="collapsible-content">
            <div class="stats-grid mb-lg" style="grid-template-columns: repeat(3, 1fr);">
                <div class="stat-card" style="border-left: 4px solid var(--accent-danger);">
                    <div class="stat-number text-danger">{{ critical_findings|length }}</div>
                    <div class="stat-label">Critical Issues</div>
                </div>
                <div class="stat-card" style="border-left: 4px solid var(--accent-warning);">
                    <div class="stat-number text-warning">{{ warning_findings|length }}</div>
                    <div class="stat-label">Warnings</div>
                </div>
                <div class="stat-card" style="border-left: 4px solid var(--accent-info);">
                    <div class="stat-number text-primary">{{ info_findings|length }}</div>
                    <div class="stat-label">Information</div>
                </div>
            </div>
            
            <!-- Critical Findings -->
            {% if critical_findings|length > 0 %}
            <div class="collapsible mb-lg">
                <div class="collapsible-header" style="background: rgba(239, 68, 68, 0.1);">
                    <h4 class="mb-0" style="color: var(--accent-danger);">🚨 Critical Security Issues ({{ critical_findings|length }})</h4>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="collapsible-content">
                    {% for match in critical_findings %}
                    <div class="card mb-sm" style="border-left: 4px solid var(--accent-danger);">
                        <div class="card-header">
                            <strong>{{ match.plugin_name or 'Security Scanner' }}</strong>
                            <span class="status status-danger">Critical</span>
                        </div>
                        <p class="mb-0">{{ match.description }}</p>
                        {% if match.matched_text and match.matched_text.strip() %}
                        <div class="code-block mt-sm">
                            <pre style="font-size: 0.8em;">{{ match.matched_text }}</pre>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Warning Findings -->
            {% if warning_findings|length > 0 %}
            <div class="collapsible mb-lg">
                <div class="collapsible-header" style="background: rgba(245, 158, 11, 0.1);">
                    <h4 class="mb-0" style="color: var(--accent-warning);">⚠️ Security Warnings ({{ warning_findings|length }})</h4>
                    <span class="collapse-icon">▼</span>
                </div>
                <div class="collapsible-content">
                    {% for match in warning_findings %}
                    <div class="card mb-sm" style="border-left: 4px solid var(--accent-warning);">
                        <div class="card-header">
                            <strong>{{ match.plugin_name or 'Security Scanner' }}</strong>
                            <span class="status status-warning">Warning</span>
                        </div>
                        <p class="mb-0">{{ match.description }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Information Findings -->
            {% if info_findings|length > 0 %}
            <div class="collapsible mb-lg collapsed">
                <div class="collapsible-header" style="background: rgba(6, 182, 212, 0.1);">
                    <h4 class="mb-0" style="color: var(--accent-info);">ℹ️ Information Discovered ({{ info_findings|length }})</h4>
                    <span class="collapse-icon">▶</span>
                </div>
                <div class="collapsible-content">
                    <div style="columns: 2; column-gap: 20px;">
                        {% for match in info_findings %}
                        <div class="mb-sm" style="break-inside: avoid;">
                            <strong>{{ match.plugin_name or 'Scanner' }}:</strong>
                            <span style="color: var(--text-secondary);">{{ match.description }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- Manual Commands -->
    {% if target_data.manual_commands and target_data.manual_commands|length > 0 %}
    {% set valid_commands = target_data.manual_commands|selectattr('command')|list %}
    {% if valid_commands|length > 0 %}
    <div class="collapsible collapsed">
        <div class="collapsible-header">
            <h3 class="mb-0">⚡ Manual Commands ({{ valid_commands|length }} commands)</h3>
            <span class="collapse-icon">▶</span>
        </div>
        <div class="collapsible-content">
            <p class="text-muted mb-lg">
                The following commands require manual execution for further investigation:
            </p>
            
            {% for command in valid_commands %}
            {% if command.command and command.command.strip() %}
            <div class="card mb-md">
                <div class="card-header">
                    <h4 class="card-title">{{ command.plugin_name or 'Unknown Plugin' }}</h4>
                    {% if command.service %}
                    <div class="card-subtitle">Service: {{ command.service }} (Port {{ command.port }})</div>
                    {% endif %}
                </div>
                
                {% if command.description and command.description.strip() %}
                <p class="mb-md">{{ command.description }}</p>
                {% endif %}
                
                <div class="code-block">
                    <div class="code-block-header">
                        <span class="code-title">Command</span>
                        <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                    </div>
                    <pre>{{ command.command }}</pre>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- Raw Scan Data -->
    {% if target_data.raw_files and target_data.raw_files|length > 0 %}
    {% set valid_files = target_data.raw_files|selectattr('name')|list %}
    {% if valid_files|length > 0 %}
    
    <!-- Highlight directory busting files -->
    {% set dir_scan_files = [] %}
    {% for file in valid_files %}
        {% if 'feroxbuster' in file.name or 'dirbuster' in file.name or 'gobuster' in file.name or 'dirb' in file.name %}
            {% set _ = dir_scan_files.append(file) %}
        {% endif %}
    {% endfor %}
    
    <div class="collapsible collapsed">
        <div class="collapsible-header">
            <h3 class="mb-0">📋 Raw Scan Data ({{ valid_files|length }} files{% if dir_scan_files|length > 0 %} - {{ dir_scan_files|length }} directory scans{% endif %})</h3>
            <span class="collapse-icon">▶</span>
        </div>
        <div class="collapsible-content">
            <p class="text-muted mb-lg">
                Raw output files generated during the scan. {% if dir_scan_files|length > 0 %}Directory busting files are highlighted below.{% endif %}
            </p>
            
            <!-- Show directory busting files first if they exist -->
            {% if dir_scan_files|length > 0 %}
            <div class="card mb-lg" style="border-left: 4px solid var(--accent-warning); background: rgba(245, 158, 11, 0.1);">
                <div class="card-header">
                    <h4 class="mb-0" style="color: var(--accent-warning);">📁 Directory/File Discovery Scans</h4>
                </div>
                <p class="mb-md">If directories/files aren't showing above, check these raw scan outputs:</p>
                {% for file in dir_scan_files %}
                <div class="collapsible collapsed mb-sm">
                    <div class="collapsible-header" style="background: var(--bg-tertiary);">
                        <h5 class="card-title mb-0">🔍 {{ file.name }}</h5>
                        <div class="text-muted">{{ file.size or 'Unknown size' }}</div>
                    </div>
                    <div class="collapsible-content">
                        {% if file.content and file.content.strip() %}
                        <div class="code-block">
                            <div class="code-block-header">
                                <span class="code-title">{{ file.name }}</span>
                                <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            </div>
                            <pre>{{ file.content }}</pre>
                        </div>
                        {% else %}
                        <p class="text-muted">File content not available or too large to display.</p>
                        {% if file.path %}
                        <p>File path: <code>{{ file.path }}</code></p>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% for file in valid_files %}
            {% if file.name and file.name.strip() %}
            <div class="collapsible collapsed mb-md">
                <div class="collapsible-header" style="background: var(--bg-tertiary);">
                    <h4 class="card-title mb-0">📄 {{ file.name }}</h4>
                    <div class="text-muted">{{ file.size or 'Unknown size' }}</div>
                </div>
                <div class="collapsible-content">
                    {% if file.content and file.content.strip() %}
                    <div class="code-block">
                        <div class="code-block-header">
                            <span class="code-title">{{ file.name }}</span>
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre>{{ file.content }}</pre>
                    </div>
                    {% else %}
                    <p class="text-muted">File content not available or too large to display.</p>
                    {% if file.path %}
                    <p>File path: <code>{{ file.path }}</code></p>
                    {% endif %}
                    {% endif %}
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- Target-specific Notes -->
    {% if target_data.notes and target_data.notes|length > 0 %}
    {% set valid_notes = target_data.notes|selectattr('content')|list %}
    {% if valid_notes|length > 0 %}
    <div class="collapsible collapsed">
        <div class="collapsible-header">
            <h3 class="mb-0">📝 Notes & Observations</h3>
            <span class="collapse-icon">▶</span>
        </div>
        <div class="collapsible-content">
            {% for note in valid_notes %}
            {% if note.content and note.content.strip() %}
            <div class="card mb-md">
                <div class="card-header">
                    <h4 class="card-title">{{ note.plugin_name or 'General' }}</h4>
                    {% if note.timestamp %}
                    <div class="card-subtitle">{{ note.timestamp }}</div>
                    {% endif %}
                </div>
                <p>{{ note.content }}</p>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endif %}
</div>